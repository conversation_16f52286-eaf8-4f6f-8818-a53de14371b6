# FileScopeMCP 实时更新保障指南

## 📋 概述

本指南提供确保 FileScopeMCP 实时更新功能正常运行的完整方案，包括原理分析、配置优化、问题排查和维护建议。

## 🔍 1. 实时更新机制原理

FileScopeMCP 使用 Node.js 文件系统监控 API 实现实时更新：

```typescript
// 核心监控机制
- fs.watch() 或 chokidar 库监听文件系统事件
- 防抖机制（默认1000ms）避免频繁触发
- 事件过滤和路径标准化
- 自动重建文件树和重新计算重要性
```

### 监控事件类型
- **文件创建** (`watchForNewFiles`)
- **文件修改** (`watchForChanged`) 
- **文件删除** (`watchForDeleted`)

## 🩺 2. 状态检查和诊断

### 检查监控状态
```bash
# 通过 MCP 工具检查
get_file_watching_status()
```

### 查看监控配置
```json
{
  "fileWatching": {
    "enabled": true,
    "debounceMs": 1000,
    "watchForNewFiles": true,
    "watchForChanged": true,
    "watchForDeleted": true,
    "autoRebuildTree": true,
    "ignoreDotFiles": true,
    "maxWatchedDirectories": 1000
  }
}
```

### 系统诊断命令
```bash
# 检查文件权限
ls -la /path/to/project

# 检查磁盘空间
df -h

# 检查 inotify 限制 (Linux)
cat /proc/sys/fs/inotify/max_user_watches
cat /proc/sys/fs/inotify/max_user_instances

# 检查当前 inotify 使用情况
find /proc/*/fd -lname anon_inode:inotify 2>/dev/null | wc -l
```

## ⚙️ 3. 配置优化

### 优化监控配置
```javascript
// 通过 MCP 工具更新配置
update_file_watching_config({
  config: {
    enabled: true,
    debounceMs: 500,           // 减少延迟，提高响应速度
    watchForNewFiles: true,
    watchForChanged: true,
    watchForDeleted: true,
    autoRebuildTree: true,
    ignoreDotFiles: true,      // 忽略隐藏文件
    maxWatchedDirectories: 2000 // 增加监控目录限制
  }
})
```

### 排除规则优化
```json
// 在 {project}-excludes.json 中添加
[
  "node_modules/**",
  ".git/**",
  ".venv/**",
  "**/*.log",
  "**/*.tmp",
  "**/coverage/**",
  "**/dist/**",
  "**/build/**",
  "**/__pycache__/**",
  "**/target/**",
  "**/*.pyc",
  "**/*.pyo"
]
```

## 🔧 4. 常见问题排查

### 问题 1: 监控未启动
```bash
# 解决方案
1. 检查监控状态
   get_file_watching_status()

2. 手动启用监控
   toggle_file_watching()

3. 重启 MCP 服务器
```

### 问题 2: 更新延迟过大
```javascript
// 减少防抖时间
update_file_watching_config({
  config: {
    debounceMs: 200  // 从 1000ms 减少到 200ms
  }
})
```

### 问题 3: 监控停止工作
```bash
# Linux: 增加 inotify 限制
echo 524288 | sudo tee /proc/sys/fs/inotify/max_user_watches
echo 128 | sudo tee /proc/sys/fs/inotify/max_user_instances

# 永久设置
echo "fs.inotify.max_user_watches=524288" | sudo tee -a /etc/sysctl.conf
echo "fs.inotify.max_user_instances=128" | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

### 问题 4: 权限问题
```bash
# 检查和修复权限
chmod -R 755 /path/to/project
chown -R $USER:$USER /path/to/project
```

## 🚀 5. 性能优化

### 减少监控负载
```javascript
// 优化配置
{
  "fileWatching": {
    "maxWatchedDirectories": 1000,  // 限制监控目录数
    "ignoreDotFiles": true,         // 忽略隐藏文件
    "debounceMs": 1000             // 适当的防抖时间
  }
}
```

### 智能排除策略
- 排除大型依赖目录 (`node_modules`, `.venv`)
- 排除构建产物目录 (`dist`, `build`, `target`)
- 排除版本控制目录 (`.git`)
- 排除临时文件 (`*.tmp`, `*.log`)
- 排除编译缓存 (`__pycache__`, `*.pyc`)

## 🖥️ 6. 平台特定注意事项

### Windows 系统
```bash
# 避免文件锁定问题
- 关闭杀毒软件实时扫描对项目目录的监控
- 确保没有其他程序锁定文件
- 使用管理员权限运行（如果需要）
- 检查 Windows Defender 排除列表
```

### Linux/macOS 系统
```bash
# 增加系统限制
# 临时设置
sudo sysctl fs.inotify.max_user_watches=524288
sudo sysctl fs.inotify.max_user_instances=128

# 永久设置
echo "fs.inotify.max_user_watches=524288" | sudo tee -a /etc/sysctl.conf
echo "fs.inotify.max_user_instances=128" | sudo tee -a /etc/sysctl.conf
```

### WSL 环境
```bash
# WSL 特殊配置
# 在 /etc/wsl.conf 中添加
[automount]
options = "metadata,umask=22,fmask=11"

# 重启 WSL
wsl --shutdown
```

### Docker 容器
```dockerfile
# Dockerfile 中增加 inotify 限制
RUN echo "fs.inotify.max_user_watches=524288" >> /etc/sysctl.conf
```

## 📊 7. 监控和维护

### 定期检查脚本
```bash
#!/bin/bash
# 每日检查脚本
echo "检查 FileScopeMCP 监控状态..."

# 检查文件树更新时间
ls -la *-tree.json

# 检查系统资源
df -h
free -h

# 检查 inotify 使用情况 (Linux)
find /proc/*/fd -lname anon_inode:inotify 2>/dev/null | wc -l
```

### 健康检查脚本
```bash
#!/bin/bash
PROJECT_DIR="/path/to/project"
TREE_FILE="$PROJECT_DIR/project-tree.json"

# 检查文件是否存在
if [ ! -f "$TREE_FILE" ]; then
    echo "ERROR: Tree file not found"
    exit 1
fi

# 检查文件更新时间（不超过5分钟）
if [ $(find "$TREE_FILE" -mmin +5) ]; then
    echo "WARNING: Tree file not updated recently"
fi

echo "FileScopeMCP health check passed"
```

## 🛠️ 8. 故障恢复

### 重启监控
```javascript
// 完全重启监控
1. toggle_file_watching()  // 关闭
2. 等待几秒
3. toggle_file_watching()  // 开启
```

### 强制重建
```javascript
// 强制重新计算和保存
recalculate_importance()
```

### 清理和重置
```bash
# 删除缓存文件重新开始
rm -f *-tree.json *-excludes.json
# 然后重新创建文件树
create_file_tree(filename: "project.json", baseDirectory: "/path/to/project")
```

## ✅ 9. 最佳实践总结

### 日常维护
1. **定期检查监控状态**：每天检查一次
2. **监控系统资源**：确保足够的内存和 inotify 限制
3. **定期更新配置**：根据项目变化调整设置
4. **备份配置文件**：保存重要的配置和数据

### 配置建议
1. **合理设置防抖时间**：平衡响应速度和性能
2. **优化排除规则**：排除不必要的文件和目录
3. **使用健康检查**：自动化监控系统状态
4. **启用详细日志**：便于问题排查

### 性能优化
1. **限制监控目录数量**：避免监控过多目录
2. **忽略临时文件**：减少不必要的事件
3. **合理设置缓存**：提高响应速度
4. **定期清理**：删除过期的缓存文件

## 🆘 10. 紧急故障处理

### 监控完全失效
```bash
1. 停止 MCP 服务器
2. 删除所有缓存文件
3. 重新启动 MCP 服务器
4. 重新创建文件树
5. 重新启用监控
```

### 系统资源耗尽
```bash
1. 检查 inotify 使用情况
2. 增加系统限制
3. 优化排除规则
4. 减少监控目录数量
5. 重启监控服务
```

---

**注意**：本指南适用于 FileScopeMCP 的所有版本。如遇到特殊问题，请参考官方文档或提交 Issue。

**更新时间**：2025-08-04
**版本**：v1.0
