# Cunzhi 记忆管理系统详细说明

## 📋 项目概述

**Cunzhi** 是一个基于 Rust + Tauri 开发的智能记忆管理系统，通过 MCP (Model Context Protocol) 为 AI 助手提供全局记忆管理功能。项目名称"存知"寓意"存储知识"，专注于为开发者和 AI 系统提供持久化的记忆存储和管理能力。

### 核心特性
- 🧠 **全局记忆管理** - 跨会话的持久化记忆存储
- 🔗 **MCP 协议支持** - 与 Claude、Cursor 等 AI 工具无缝集成
- 📱 **跨平台支持** - Windows、macOS、Linux 全平台兼容
- 🎯 **分类记忆** - 支持规则、偏好、模式、上下文等多种记忆类型
- 💾 **本地存储** - 数据完全本地化，保护隐私安全
- 🚀 **高性能** - Rust 后端确保高效的内存和 I/O 操作

## 🏗️ 系统架构

### 技术栈
```
前端: HTML + CSS + JavaScript (Tauri WebView)
后端: Rust (Tauri + Tokio)
协议: MCP (Model Context Protocol)
存储: 本地文件系统 (JSON/SQLite)
```

### 核心模块
```
src/rust/
├── mcp/                    # MCP 协议实现
│   ├── tools/memory/       # 记忆管理核心
│   ├── server.rs          # MCP 服务器
│   └── commands.rs        # 命令处理
├── config/                # 配置管理
│   ├── settings.rs        # 应用设置
│   └── storage.rs         # 存储配置
└── ui/                    # 用户界面
```

## 🧠 记忆管理核心机制

### 记忆类型分类

#### 1. **规则记忆 (Rule)**
```rust
// 存储开发规范、编码标准等
{
    "category": "rule",
    "content": "使用 TypeScript 严格模式，所有函数必须有类型注解",
    "project_path": "/path/to/project",
    "timestamp": "2025-08-04T10:00:00Z"
}
```

#### 2. **偏好记忆 (Preference)**
```rust
// 存储用户偏好、习惯等
{
    "category": "preference", 
    "content": "优先使用函数式编程风格，避免类继承",
    "project_path": "/path/to/project",
    "timestamp": "2025-08-04T10:00:00Z"
}
```

#### 3. **模式记忆 (Pattern)**
```rust
// 存储最佳实践、设计模式等
{
    "category": "pattern",
    "content": "API 错误处理使用 Result<T, E> 模式",
    "project_path": "/path/to/project", 
    "timestamp": "2025-08-04T10:00:00Z"
}
```

#### 4. **上下文记忆 (Context)**
```rust
// 存储项目特定的上下文信息
{
    "category": "context",
    "content": "当前项目使用微服务架构，服务间通过 gRPC 通信",
    "project_path": "/path/to/project",
    "timestamp": "2025-08-04T10:00:00Z"
}
```

### 记忆管理器 (MemoryManager)

#### 核心数据结构
```rust
pub struct MemoryManager {
    memories: HashMap<String, Vec<Memory>>,  // 按项目路径分组的记忆
    storage_path: PathBuf,                   // 存储路径
}

pub struct Memory {
    pub id: String,           // 唯一标识符
    pub category: String,     // 记忆类别
    pub content: String,      // 记忆内容
    pub project_path: String, // 项目路径
    pub timestamp: DateTime<Utc>, // 创建时间
    pub tags: Vec<String>,    // 标签
}
```

#### 核心方法
```rust
impl MemoryManager {
    // 添加记忆
    pub fn add_memory(&mut self, memory: Memory) -> Result<(), MemoryError>
    
    // 检索记忆
    pub fn recall_memories(&self, project_path: &str, category: Option<&str>) -> Vec<&Memory>
    
    // 搜索记忆
    pub fn search_memories(&self, query: &str, project_path: &str) -> Vec<&Memory>
    
    // 删除记忆
    pub fn remove_memory(&mut self, memory_id: &str) -> Result<(), MemoryError>
    
    // 持久化存储
    pub fn save_to_disk(&self) -> Result<(), MemoryError>
    
    // 从磁盘加载
    pub fn load_from_disk(&mut self) -> Result<(), MemoryError>
}
```

## 🔧 MCP 工具接口

### 1. **记忆工具 (ji___)**
```rust
// 工具名称: ji___ (记忆的拼音缩写)
// 功能: 添加和检索记忆

// 添加记忆
{
    "action": "记忆",
    "category": "rule|preference|pattern|context", 
    "content": "记忆内容",
    "project_path": "/path/to/project"
}

// 检索记忆  
{
    "action": "回忆",
    "project_path": "/path/to/project",
    "category": "rule" // 可选，指定类别
}
```

### 2. **智能代码审查工具 (zhi___)**
```rust
// 工具名称: zhi___ (智能的拼音缩写)
// 功能: 智能代码审查和交互

{
    "message": "要显示给用户的消息",
    "predefined_options": ["选项1", "选项2"], // 可选
    "is_markdown": true // 可选，默认true
}
```

## 💾 存储机制

### 存储结构
```
~/.cunzhi/
├── memories/
│   ├── global.json         # 全局记忆
│   └── projects/
│       ├── project1.json   # 项目特定记忆
│       └── project2.json
├── config/
│   ├── settings.json       # 应用配置
│   └── storage.json        # 存储配置
└── logs/
    └── app.log            # 应用日志
```

### 存储配置
```rust
pub struct StorageConfig {
    pub base_path: PathBuf,           // 基础存储路径
    pub auto_save: bool,              // 自动保存
    pub backup_enabled: bool,         // 备份启用
    pub max_backup_files: usize,      // 最大备份文件数
    pub compression_enabled: bool,     // 压缩启用
}
```

### 数据持久化
- **格式**: JSON (人类可读，便于调试)
- **备份**: 自动创建备份文件
- **压缩**: 可选的数据压缩
- **加密**: 计划中的数据加密功能

## 🚀 使用示例

### 基本使用流程

#### 1. 添加开发规范记忆
```javascript
// 通过 MCP 工具调用
ji___({
    action: "记忆",
    category: "rule", 
    content: "所有 API 接口必须包含错误处理和日志记录",
    project_path: "/home/<USER>/my-project"
})
```

#### 2. 添加用户偏好
```javascript
ji___({
    action: "记忆",
    category: "preference",
    content: "优先使用 async/await 而不是 Promise.then()",
    project_path: "/home/<USER>/my-project"
})
```

#### 3. 检索项目记忆
```javascript
ji___({
    action: "回忆", 
    project_path: "/home/<USER>/my-project"
})
```

#### 4. 检索特定类别记忆
```javascript
ji___({
    action: "回忆",
    project_path: "/home/<USER>/my-project", 
    category: "rule"
})
```

### 高级功能

#### 记忆搜索
```rust
// 基于关键词搜索记忆
memory_manager.search_memories("API 错误处理", "/path/to/project")
```

#### 记忆标签
```rust
// 为记忆添加标签便于分类
Memory {
    content: "使用 Repository 模式管理数据访问",
    tags: vec!["设计模式", "数据库", "架构"],
    // ...
}
```

#### 记忆过期
```rust
// 设置记忆过期时间
Memory {
    content: "临时解决方案：使用硬编码配置",
    expires_at: Some(Utc::now() + Duration::days(7)),
    // ...
}
```

## 🔒 安全和隐私

### 数据安全
- **本地存储**: 所有数据存储在本地，不上传到云端
- **文件权限**: 严格的文件系统权限控制
- **数据验证**: 输入数据的严格验证和清理

### 隐私保护
- **无网络传输**: 记忆数据不通过网络传输
- **用户控制**: 用户完全控制数据的创建、修改和删除
- **透明存储**: JSON 格式便于用户查看和管理

## 🛠️ 安装和配置

### 系统要求
- **操作系统**: Windows 10+, macOS 10.15+, Linux (Ubuntu 18.04+)
- **内存**: 最少 512MB RAM
- **存储**: 最少 100MB 可用空间

### 安装方法

#### 方法 1: 预编译二进制
```bash
# Linux/macOS
curl -sSL https://github.com/imhuso/cunzhi/raw/main/install-universal.sh | bash

# Windows (PowerShell)
iwr -useb https://github.com/imhuso/cunzhi/raw/main/install-windows.ps1 | iex
```

#### 方法 2: 从源码编译
```bash
# 克隆仓库
git clone https://github.com/imhuso/cunzhi.git
cd cunzhi

# 安装依赖
cargo build --release

# 运行
./target/release/cunzhi
```

### MCP 配置
```json
// 在 Claude Desktop 或 Cursor 的配置文件中添加
{
  "mcpServers": {
    "cunzhi": {
      "command": "/path/to/cunzhi",
      "args": ["--mcp"],
      "transport": "stdio"
    }
  }
}
```

## 📈 性能特性

### 内存使用
- **轻量级**: 基础内存占用 < 50MB
- **增量加载**: 按需加载项目记忆
- **内存池**: 高效的内存管理

### I/O 性能
- **异步 I/O**: 基于 Tokio 的异步文件操作
- **批量操作**: 支持批量记忆操作
- **缓存机制**: 智能缓存减少磁盘访问

### 扩展性
- **项目隔离**: 不同项目的记忆独立存储
- **分片存储**: 大型项目的记忆分片存储
- **索引优化**: 快速检索和搜索

## 🔄 工作流程

### 记忆生命周期
```mermaid
graph TD
    A[用户输入] --> B[记忆创建]
    B --> C[类别分类]
    C --> D[项目关联]
    D --> E[持久化存储]
    E --> F[索引更新]
    F --> G[记忆可用]
    G --> H[检索使用]
    H --> I[记忆更新/删除]
    I --> E
```

### 典型使用场景

#### 场景 1: 新项目开发
```bash
1. 创建项目记忆空间
2. 添加项目规范和约定
3. 记录技术选型决策
4. 存储最佳实践模式
5. AI 助手基于记忆提供建议
```

#### 场景 2: 代码审查
```bash
1. AI 助手检索项目规范记忆
2. 对比代码与已知模式
3. 提供改进建议
4. 记录新的最佳实践
5. 更新项目记忆库
```

#### 场景 3: 问题解决
```bash
1. 检索相关历史记忆
2. 查找类似问题的解决方案
3. 应用已知模式
4. 记录新的解决方案
5. 丰富项目知识库
```

## 🎯 高级特性

### 智能记忆推荐
```rust
// 基于上下文的智能记忆推荐
impl MemoryManager {
    pub fn recommend_memories(&self, context: &str, project_path: &str) -> Vec<&Memory> {
        // 基于语义相似度推荐相关记忆
        // 考虑记忆的使用频率和时效性
        // 返回最相关的记忆列表
    }
}
```

### 记忆关联分析
```rust
// 分析记忆之间的关联关系
pub struct MemoryGraph {
    nodes: HashMap<String, Memory>,
    edges: HashMap<String, Vec<String>>,
}

impl MemoryGraph {
    pub fn find_related_memories(&self, memory_id: &str) -> Vec<&Memory> {
        // 基于图算法找到相关记忆
    }
}
```

### 自动记忆提取
```rust
// 从代码和文档中自动提取记忆
pub trait MemoryExtractor {
    fn extract_from_code(&self, code: &str) -> Vec<Memory>;
    fn extract_from_docs(&self, docs: &str) -> Vec<Memory>;
    fn extract_from_commits(&self, commits: &[Commit]) -> Vec<Memory>;
}
```

## 🔧 配置详解

### 应用配置 (settings.json)
```json
{
  "memory": {
    "auto_save_interval": 300,        // 自动保存间隔(秒)
    "max_memories_per_project": 1000, // 每项目最大记忆数
    "enable_smart_suggestions": true,  // 启用智能建议
    "memory_retention_days": 365      // 记忆保留天数
  },
  "storage": {
    "compression_level": 6,           // 压缩级别 (0-9)
    "backup_frequency": "daily",      // 备份频率
    "max_backup_size_mb": 100        // 最大备份大小
  },
  "mcp": {
    "server_port": 8080,             // MCP 服务器端口
    "max_concurrent_requests": 10,    // 最大并发请求
    "request_timeout_ms": 5000       // 请求超时时间
  },
  "ui": {
    "theme": "auto",                 // 主题: light/dark/auto
    "language": "zh-CN",             // 界面语言
    "show_notifications": true       // 显示通知
  }
}
```

### 存储配置 (storage.json)
```json
{
  "base_path": "~/.cunzhi",
  "auto_save": true,
  "backup_enabled": true,
  "max_backup_files": 10,
  "compression_enabled": true,
  "encryption_enabled": false,
  "index_enabled": true,
  "cache_size_mb": 50
}
```

## 🐛 故障排除

### 常见问题

#### 1. MCP 连接失败
```bash
# 检查进程是否运行
ps aux | grep cunzhi

# 检查端口是否被占用
netstat -tulpn | grep 8080

# 重启服务
cunzhi --restart
```

#### 2. 记忆保存失败
```bash
# 检查磁盘空间
df -h ~/.cunzhi

# 检查文件权限
ls -la ~/.cunzhi/memories/

# 修复权限
chmod -R 755 ~/.cunzhi
```

#### 3. 性能问题
```bash
# 清理过期记忆
cunzhi --cleanup

# 重建索引
cunzhi --reindex

# 压缩数据库
cunzhi --compress
```

### 日志分析
```bash
# 查看应用日志
tail -f ~/.cunzhi/logs/app.log

# 查看错误日志
grep ERROR ~/.cunzhi/logs/app.log

# 查看性能日志
grep PERF ~/.cunzhi/logs/app.log
```

## 🚀 开发和扩展

### 插件开发
```rust
// 自定义记忆处理器
pub trait MemoryProcessor {
    fn process(&self, memory: &mut Memory) -> Result<(), ProcessError>;
    fn validate(&self, memory: &Memory) -> Result<(), ValidationError>;
}

// 注册插件
impl MemoryManager {
    pub fn register_processor(&mut self, processor: Box<dyn MemoryProcessor>) {
        self.processors.push(processor);
    }
}
```

### API 扩展
```rust
// 自定义 MCP 工具
#[derive(Debug, Serialize, Deserialize)]
pub struct CustomTool {
    pub name: String,
    pub description: String,
    pub parameters: serde_json::Value,
}

impl McpTool for CustomTool {
    fn execute(&self, params: &serde_json::Value) -> Result<serde_json::Value, McpError> {
        // 自定义工具逻辑
    }
}
```

### 数据迁移
```rust
// 版本迁移支持
pub trait DataMigrator {
    fn migrate_from_version(&self, from: &str, to: &str) -> Result<(), MigrationError>;
    fn backup_before_migration(&self) -> Result<PathBuf, MigrationError>;
    fn rollback_migration(&self, backup_path: &Path) -> Result<(), MigrationError>;
}
```

## 📊 监控和分析

### 性能指标
```rust
pub struct PerformanceMetrics {
    pub memory_usage_mb: f64,
    pub disk_usage_mb: f64,
    pub avg_response_time_ms: f64,
    pub total_memories: usize,
    pub active_projects: usize,
    pub cache_hit_rate: f64,
}
```

### 使用统计
```rust
pub struct UsageStats {
    pub memories_created_today: usize,
    pub memories_accessed_today: usize,
    pub most_used_categories: Vec<(String, usize)>,
    pub active_projects: Vec<String>,
    pub search_queries: Vec<String>,
}
```

## 🔮 未来规划

### 短期目标 (3个月)
- [ ] 记忆自动分类和标签
- [ ] 智能记忆推荐算法
- [ ] 多语言支持 (英文、中文)
- [ ] 记忆导入/导出功能
- [ ] 性能优化和缓存改进

### 中期目标 (6个月)
- [ ] 记忆可视化界面
- [ ] 团队协作功能
- [ ] 云同步支持
- [ ] 记忆版本控制
- [ ] 高级搜索和过滤

### 长期目标 (1年)
- [ ] AI 驱动的记忆管理
- [ ] 跨项目记忆关联
- [ ] 记忆质量评估
- [ ] 企业级部署支持
- [ ] 开放 API 生态

---

**项目地址**: https://github.com/imhuso/cunzhi
**更新时间**: 2025-08-04
**版本**: v1.0
**文档版本**: v1.0.1
